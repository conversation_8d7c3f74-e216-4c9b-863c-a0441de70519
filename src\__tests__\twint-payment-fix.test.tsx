import { render, screen, waitFor } from '@testing-library/react'
import { useSearchParams, useRouter } from 'next/navigation'
import { useTranslations, useLocale } from 'next-intl'
import CheckoutSuccessPage from '@/app/[locale]/checkout/success/page'

// Mock the dependencies
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
  useRouter: jest.fn(),
}))

jest.mock('next-intl', () => ({
  useTranslations: jest.fn(),
  useLocale: jest.fn(),
}))

// Mock fetch globally
global.fetch = jest.fn()

const mockUseSearchParams = useSearchParams as jest.MockedFunction<typeof useSearchParams>
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>
const mockUseTranslations = useTranslations as jest.MockedFunction<typeof useTranslations>
const mockUseLocale = useLocale as jest.MockedFunction<typeof useLocale>

describe('TWINT Payment Fix', () => {
  const mockPush = jest.fn()
  const mockT = jest.fn((key: string) => key)

  beforeEach(() => {
    jest.clearAllMocks()
    
    mockUseRouter.mockReturnValue({
      push: mockPush,
      back: jest.fn(),
      forward: jest.fn(),
      refresh: jest.fn(),
      replace: jest.fn(),
      prefetch: jest.fn(),
    })
    
    mockUseTranslations.mockReturnValue(mockT)
    mockUseLocale.mockReturnValue('it')
    
    // Mock successful fetch responses
    ;(global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: () => Promise.resolve({
        success: true,
        orderId: 'test-order-123'
      })
    })
  })

  it('should handle TWINT payment with client secret but no payment_intent ID', async () => {
    const mockSearchParams = new Map([
      ['payment_intent_client_secret', 'pi_test123_secret_abc123'],
      ['redirect_status', 'succeeded']
    ])

    mockUseSearchParams.mockReturnValue({
      get: (key: string) => mockSearchParams.get(key) || null,
      entries: () => mockSearchParams.entries(),
    } as any)

    render(<CheckoutSuccessPage />)

    // Should show loading initially
    expect(screen.getByText('confirmingPayment')).toBeInTheDocument()

    // Wait for the component to process the client secret
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/checkout/confirm-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentIntentId: 'pi_test123' }),
      })
    })
  })

  it('should show TWINT success message when redirect_status is succeeded but no payment_intent', async () => {
    const mockSearchParams = new Map([
      ['redirect_status', 'succeeded']
    ])

    mockUseSearchParams.mockReturnValue({
      get: (key: string) => mockSearchParams.get(key) || null,
      entries: () => mockSearchParams.entries(),
    } as any)

    render(<CheckoutSuccessPage />)

    // Should show TWINT success message
    await waitFor(() => {
      expect(screen.getByText('paymentSuccessful')).toBeInTheDocument()
      expect(screen.getByText('twintPaymentSuccess')).toBeInTheDocument()
      expect(screen.getByText('continueShopping')).toBeInTheDocument()
    })
  })

  it('should handle normal payment_intent parameter correctly', async () => {
    const mockSearchParams = new Map([
      ['payment_intent', 'pi_test456'],
      ['redirect_status', 'succeeded']
    ])

    mockUseSearchParams.mockReturnValue({
      get: (key: string) => mockSearchParams.get(key) || null,
      entries: () => mockSearchParams.entries(),
    } as any)

    render(<CheckoutSuccessPage />)

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith('/api/checkout/confirm-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ paymentIntentId: 'pi_test456' }),
      })
    })
  })

  it('should redirect to checkout with canceled=true when payment is canceled', async () => {
    const mockSearchParams = new Map([
      ['redirect_status', 'canceled']
    ])

    mockUseSearchParams.mockReturnValue({
      get: (key: string) => mockSearchParams.get(key) || null,
      entries: () => mockSearchParams.entries(),
    } as any)

    render(<CheckoutSuccessPage />)

    await waitFor(() => {
      expect(mockPush).toHaveBeenCalledWith('/it/checkout?canceled=true')
    })
  })

  it('should log all available parameters for debugging', async () => {
    const consoleSpy = jest.spyOn(console, 'log').mockImplementation()
    
    const mockSearchParams = new Map([
      ['some_param', 'some_value'],
      ['redirect_status', 'succeeded']
    ])

    mockUseSearchParams.mockReturnValue({
      get: (key: string) => mockSearchParams.get(key) || null,
      entries: () => mockSearchParams.entries(),
    } as any)

    render(<CheckoutSuccessPage />)

    await waitFor(() => {
      expect(consoleSpy).toHaveBeenCalledWith(
        '🛒 Success: Available URL parameters:',
        expect.objectContaining({
          all_params: expect.any(Object)
        })
      )
    })

    consoleSpy.mockRestore()
  })
})
